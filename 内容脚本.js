// 吴 - 内容脚本（简化版）
// 防止重复声明
if (typeof window.AdPopupKiller !== 'undefined') {
    console.log('AdPopupKiller已存在，跳过重复加载');
    return;
}

class AdPopupKiller {
    constructor() {
        this.scanInterval = null;
        this.closedCount = 0;

        // 支持所有弹窗的关闭按钮选择器
        this.closeButtonSelectors = [
            // 基础选择器 - 覆盖所有版本
            'svg[data-testid="beast-core-modal-icon-close"]',
            'svg[data-testid="beast-core-icon-close"]',
            'button[data-testid="beast-core-modal-icon-close"]',
            'button[data-testid="beast-core-icon-close"]',
            // 类名选择器 - 5-118-0版本
            '.MDL_headerCloseIcon_5-118-0',
            '.ICN_outerWrapper_5-118-0',
            '.ICN_svgIcon_5-118-0',
            // 类名选择器 - 5-119-0版本
            '.MDL_headerCloseIcon_5-119-0',
            '.ICN_outerWrapper_5-119-0',
            '.ICN_svgIcon_5-119-0',
            // modal-content变体
            '.modal-content_closeIcon__2C8Fb',
            '.modal-content_closeIcon__Gw9DZ',
            '.modal-content_closeIcon__7mkdd',
            // 通用关闭按钮选择器
            'button[aria-label*="close"]',
            'button[aria-label*="Close"]',
            'button[title*="close"]',
            'button[title*="Close"]',
            '[role="button"][aria-label*="close"]',
            '[role="button"][aria-label*="Close"]'
        ];

        this.init();
    }
    
    init() {
        console.log('吴已启动');
        this.startScanning();
    }
    
    startScanning() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }

        this.scanInterval = setInterval(() => {
            this.scanAndClosePopups();
        }, 500); // 每0.5秒扫描一次
    }
    
    scanAndClosePopups() {
        try {
            // 查找并关闭弹窗
            const closeButtons = this.findCloseButtons();

            closeButtons.forEach(button => {
                if (this.isVisibleAndClickable(button)) {
                    this.clickCloseButton(button);
                }
            });

        } catch (error) {
            console.error('扫描弹窗时出错:', error);
        }
    }
    
    findCloseButtons() {
        const buttons = [];

        // 使用选择器查找关闭按钮
        this.closeButtonSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    // 查找可点击的元素（自身或父元素）
                    const clickableElement = this.findClickableElement(element);
                    if (clickableElement && !buttons.includes(clickableElement)) {
                        buttons.push(clickableElement);
                    }
                });
            } catch (e) {
                // 忽略无效的选择器
            }
        });

        return buttons;
    }

    findClickableElement(element) {
        // 检查当前元素是否可点击
        if (this.isClickableElement(element)) {
            return element;
        }

        // 向上查找可点击的父元素（最多查找5层）
        let parent = element.parentElement;
        let depth = 0;
        while (parent && depth < 5) {
            if (this.isClickableElement(parent)) {
                return parent;
            }
            parent = parent.parentElement;
            depth++;
        }

        // 如果没找到可点击的父元素，返回原元素
        return element;
    }

    isClickableElement(element) {
        if (!element) return false;

        // 检查是否是按钮或有点击事件的元素
        const tagName = element.tagName.toLowerCase();
        const hasClickHandler = typeof element.click === 'function';
        const hasRole = element.getAttribute('role') === 'button';
        const isButton = tagName === 'button';
        const hasClickListener = element.onclick !== null;

        return hasClickHandler || isButton || hasRole || hasClickListener;
    }


    
    isVisibleAndClickable(element) {
        if (!element) return false;

        // 检查元素是否可见
        const style = window.getComputedStyle(element);
        if (style.display === 'none' ||
            style.visibility === 'hidden' ||
            style.opacity === '0') {
            return false;
        }

        // 检查元素是否在视口内
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) {
            return false;
        }

        return true;
    }
    

    
    clickCloseButton(button) {
        try {
            console.log('发现弹窗关闭按钮，正在点击...', button);

            // 多种点击方式尝试
            if (typeof button.click === 'function') {
                // 标准点击方法
                button.click();
            } else {
                // 对于SVG或其他元素，尝试模拟点击事件
                const clickEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                button.dispatchEvent(clickEvent);
            }

            this.closedCount++;
            console.log(`已关闭 ${this.closedCount} 个弹窗`);

        } catch (error) {
            console.error('点击关闭按钮时出错:', error);
            // 尝试点击父元素
            try {
                if (button.parentElement && typeof button.parentElement.click === 'function') {
                    console.log('尝试点击父元素...');
                    button.parentElement.click();
                    this.closedCount++;
                    console.log(`通过父元素已关闭 ${this.closedCount} 个弹窗`);
                }
            } catch (parentError) {
                console.error('点击父元素也失败:', parentError);
            }
        }
    }

    destroy() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }
    }
}

// 将类添加到全局作用域以防止重复声明
window.AdPopupKiller = AdPopupKiller;

// 创建实例（防止重复创建）
if (!window.adPopupKillerInstance) {
    window.adPopupKillerInstance = new AdPopupKiller();

    // 页面卸载时清理
    window.addEventListener('beforeunload', () => {
        if (window.adPopupKillerInstance) {
            window.adPopupKillerInstance.destroy();
            window.adPopupKillerInstance = null;
        }
    });
}
