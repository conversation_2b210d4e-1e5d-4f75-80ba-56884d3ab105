// 吴 - 内容脚本（简化版）
// 防止重复声明
if (typeof window.AdPopupKiller !== 'undefined') {
    console.log('AdPopupKiller已存在，跳过重复加载');
    return;
}

class AdPopupKiller {
    constructor() {
        this.scanInterval = null;
        this.closedCount = 0;

        // 支持所有弹窗的关闭按钮选择器
        this.closeButtonSelectors = [
            // 基础选择器 - 覆盖所有版本
            'svg[data-testid="beast-core-modal-icon-close"]',
            'svg[data-testid="beast-core-icon-close"]',
            'button[data-testid="beast-core-modal-icon-close"]',
            'button[data-testid="beast-core-icon-close"]',
            // 类名选择器 - 5-118-0版本
            '.MDL_headerCloseIcon_5-118-0',
            '.ICN_outerWrapper_5-118-0',
            '.ICN_svgIcon_5-118-0',
            // 类名选择器 - 5-119-0版本
            '.MDL_headerCloseIcon_5-119-0',
            '.ICN_outerWrapper_5-119-0',
            '.ICN_svgIcon_5-119-0',
            // modal-content变体
            '.modal-content_closeIcon__2C8Fb',
            '.modal-content_closeIcon__Gw9DZ',
            '.modal-content_closeIcon__7mkdd',
            // 通用关闭按钮选择器
            'button[aria-label*="close"]',
            'button[aria-label*="Close"]',
            'button[title*="close"]',
            'button[title*="Close"]',
            '[role="button"][aria-label*="close"]',
            '[role="button"][aria-label*="Close"]'
        ];

        this.init();
    }
    
    init() {
        console.log('吴已启动');
        this.startScanning();
    }
    
    startScanning() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }

        this.scanInterval = setInterval(() => {
            this.scanAndClosePopups();
        }, 500); // 每0.5秒扫描一次
    }
    
    scanAndClosePopups() {
        try {
            // 查找并关闭弹窗
            const closeButtons = this.findCloseButtons();

            closeButtons.forEach(button => {
                if (this.isVisibleAndClickable(button)) {
                    this.clickCloseButton(button);
                }
            });

        } catch (error) {
            console.error('扫描弹窗时出错:', error);
        }
    }
    
    findCloseButtons() {
        const buttons = [];

        // 使用选择器查找关闭按钮
        this.closeButtonSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    // 查找可点击的元素（自身或父元素）
                    const clickableElement = this.findClickableElement(element);
                    if (clickableElement && !buttons.includes(clickableElement)) {
                        buttons.push(clickableElement);
                    }
                });
            } catch (e) {
                // 忽略无效的选择器
            }
        });

        return buttons;
    }

    findClickableElement(element) {
        // 检查当前元素是否可点击
        if (this.isClickableElement(element)) {
            return element;
        }

        // 向上查找可点击的父元素（最多查找5层）
        let parent = element.parentElement;
        let depth = 0;
        while (parent && depth < 5) {
            if (this.isClickableElement(parent)) {
                return parent;
            }
            parent = parent.parentElement;
            depth++;
        }

        // 如果没找到可点击的父元素，返回原元素
        return element;
    }

    isClickableElement(element) {
        if (!element) return false;

        try {
            // 检查是否是按钮或有点击事件的元素
            const tagName = element.tagName.toLowerCase();
            const hasRole = element.getAttribute('role') === 'button';
            const isButton = tagName === 'button';
            const hasClickListener = element.onclick !== null;

            // 安全地检查click方法
            let hasClickHandler = false;
            try {
                hasClickHandler = typeof element.click === 'function';
            } catch (e) {
                // 某些元素可能无法访问click属性
                hasClickHandler = false;
            }

            // 检查是否有cursor: pointer样式
            const style = window.getComputedStyle(element);
            const hasCursorPointer = style.cursor === 'pointer';

            // 检查是否有点击相关的属性
            const hasClickableAttributes = element.hasAttribute('onclick') ||
                                         element.hasAttribute('data-testid') ||
                                         element.classList.contains('close') ||
                                         element.classList.contains('Close');

            return hasClickHandler || isButton || hasRole || hasClickListener ||
                   hasCursorPointer || hasClickableAttributes;
        } catch (error) {
            console.warn('检查可点击元素时出错:', error);
            return false;
        }
    }


    
    isVisibleAndClickable(element) {
        if (!element) return false;

        // 检查元素是否可见
        const style = window.getComputedStyle(element);
        if (style.display === 'none' ||
            style.visibility === 'hidden' ||
            style.opacity === '0') {
            return false;
        }

        // 检查元素是否在视口内
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) {
            return false;
        }

        return true;
    }
    

    
    clickCloseButton(button) {
        try {
            console.log('发现弹窗关闭按钮，正在点击...', button);

            // 尝试多种点击方式
            let clickSuccess = false;

            // 方法1: 检查并使用标准click方法
            if (button && typeof button.click === 'function') {
                try {
                    button.click();
                    clickSuccess = true;
                    console.log('使用标准click方法成功');
                } catch (clickError) {
                    console.warn('标准click方法失败:', clickError);
                }
            }

            // 方法2: 如果标准方法失败，使用事件分发
            if (!clickSuccess) {
                try {
                    const clickEvent = new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        button: 0
                    });
                    button.dispatchEvent(clickEvent);
                    clickSuccess = true;
                    console.log('使用事件分发成功');
                } catch (eventError) {
                    console.warn('事件分发失败:', eventError);
                }
            }

            // 方法3: 尝试点击父元素
            if (!clickSuccess && button.parentElement) {
                try {
                    if (typeof button.parentElement.click === 'function') {
                        button.parentElement.click();
                        clickSuccess = true;
                        console.log('点击父元素成功');
                    } else {
                        // 对父元素也使用事件分发
                        const clickEvent = new MouseEvent('click', {
                            view: window,
                            bubbles: true,
                            cancelable: true,
                            button: 0
                        });
                        button.parentElement.dispatchEvent(clickEvent);
                        clickSuccess = true;
                        console.log('父元素事件分发成功');
                    }
                } catch (parentError) {
                    console.warn('点击父元素失败:', parentError);
                }
            }

            if (clickSuccess) {
                this.closedCount++;
                console.log(`已关闭 ${this.closedCount} 个弹窗`);
            } else {
                console.warn('所有点击方法都失败了');
            }

        } catch (error) {
            console.error('点击关闭按钮时出错:', error);
        }
    }

    destroy() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }
    }
}

// 将类添加到全局作用域以防止重复声明
window.AdPopupKiller = AdPopupKiller;

// 创建实例（防止重复创建）
if (!window.adPopupKillerInstance) {
    window.adPopupKillerInstance = new AdPopupKiller();

    // 页面卸载时清理
    window.addEventListener('beforeunload', () => {
        if (window.adPopupKillerInstance) {
            window.adPopupKillerInstance.destroy();
            window.adPopupKillerInstance = null;
        }
    });
}
